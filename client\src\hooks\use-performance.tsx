import { useEffect, useState } from 'react';
import { useIsMobile } from './use-mobile';

interface PerformanceMetrics {
  fps: number;
  memoryUsage: number;
  isLowPerformance: boolean;
}

export function usePerformance() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    memoryUsage: 0,
    isLowPerformance: false
  });
  const isMobile = useIsMobile();

  useEffect(() => {
    if (!isMobile) return; // Only monitor on mobile

    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        // Get memory usage if available
        const memory = (performance as any).memory;
        const memoryUsage = memory ? memory.usedJSHeapSize / memory.jsHeapSizeLimit : 0;
        
        // Consider low performance if FPS < 30 or memory usage > 80%
        const isLowPerformance = fps < 30 || memoryUsage > 0.8;
        
        setMetrics({
          fps,
          memoryUsage: memoryUsage * 100,
          isLowPerformance
        });
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };

    animationId = requestAnimationFrame(measureFPS);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [isMobile]);

  return metrics;
}
