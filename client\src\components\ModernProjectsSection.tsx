import { ExternalLink, Gith<PERSON>, Star, TrendingUp, Brain, Globe, Database, Calendar, Award } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";

const projects = [
  {
    id: 1,
    title: "DCI Workflow Platform",
    category: "Full-Stack Application",
    description: "Enterprise workflow management system built from ground up with React.js frontend, Node.js backend, and MySQL database. Features user authentication, role-based access, and real-time updates.",
    technologies: ["React.js", "Node.js", "MySQL", "Express.js", "REST APIs"],
    status: "Production",
    company: "Digital Creative International",
    duration: "Dec 2024 - Present",
    metrics: ["20K+ Lines of Code", "Multiple User Roles", "Real-time Updates"],
    featured: true,
    color: "border-[hsl(191,100%,50%)]",
    bgColor: "from-[hsl(191,100%,50%)]/10",
    icon: Globe,
    image: "https://images.unsplash.com/photo-**********-e076c223a692?w=600&h=400&fit=crop&auto=format&q=80"
  },
  {
    id: 2,
    title: "Lung Cancer Detection CNN",
    category: "Machine Learning Research",
    description: "Deep learning model using VGG16 architecture for medical image analysis. Achieved 98% accuracy in lung cancer detection from CT scans. Research published in IEEE conference proceedings.",
    technologies: ["Python", "TensorFlow", "VGG16", "OpenCV", "NumPy"],
    status: "Published",
    company: "IEEE Research Publication",
    duration: "Jan 2024 - Jun 2024",
    metrics: ["98% Accuracy", "IEEE Published", "Medical Impact"],
    featured: true,
    color: "border-[hsl(261,100%,67%)]",
    bgColor: "from-[hsl(261,100%,67%)]/10",
    icon: Brain,
    image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=600&h=400&fit=crop&auto=format&q=80"
  },
  {
    id: 3,
    title: "krishnaramyadav.in",
    category: "Portfolio Website",
    description: "Personal portfolio website showcasing projects and skills. Built with modern web technologies, featuring responsive design, dark theme, and optimized performance.",
    technologies: ["HTML5", "CSS3", "JavaScript", "Responsive Design"],
    status: "Live",
    company: "Personal Project",
    duration: "Oct 2023 - Nov 2023",
    metrics: ["Responsive Design", "Dark Theme", "Fast Loading"],
    featured: false,
    color: "border-[hsl(213,100%,50%)]",
    bgColor: "from-[hsl(213,100%,50%)]/10",
    icon: Database,
    image: "https://images.unsplash.com/photo-**********-da2b51169166?w=600&h=400&fit=crop&auto=format&q=80"
  }
];

export default function ModernProjectsSection() {
  const isMobile = useIsMobile();

  return (
    <section id="projects" className="py-12 sm:py-20 relative">
      <div className="container mx-auto px-4 sm:px-6">
        {/* Section Header */}
        <div className="text-center mb-8 sm:mb-16">
          <h2 className="font-orbitron text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black mb-4 sm:mb-6 tracking-wider">
            <span className="bg-gradient-to-r from-[#00FF9F] via-[#FFD700] to-[#FF6B9D] bg-clip-text text-transparent">
              FEATURED
            </span>
            <span className="text-white/90 ml-1 sm:ml-2">_PROJECTS</span>
          </h2>
          <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-300 max-w-2xl mx-auto px-4">
            Showcasing innovative solutions from enterprise platforms to cutting-edge research
          </p>
        </div>

        {/* Featured Projects */}
        <div className="space-y-6 sm:space-y-8 lg:space-y-12 mb-8 sm:mb-16">
          {projects.filter(project => project.featured).map((project, index) => {
            const IconComponent = project.icon;
            return (
              <div 
                key={project.id}
                className={`glass-effect rounded-3xl border ${project.color}/30 overflow-hidden group hover:border-${project.color.split('-')[1]}-400/60 transition-all duration-300`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="grid lg:grid-cols-2 gap-0">
                  {/* Content Side */}
                  <div className="p-4 sm:p-6 lg:p-8 xl:p-12 space-y-4 sm:space-y-6">
                    {/* Header */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-gradient-to-br ${project.bgColor} to-transparent border border-${project.color.split('-')[1]}-400/30 flex items-center justify-center`}>
                          <IconComponent className={project.color.replace('border-', 'text-')} size={20} />
                        </div>
                        <div>
                          <div className="flex items-center space-x-2 mb-1">
                            <span className={`px-3 py-1 rounded-full text-xs font-fira ${project.color.replace('border-', 'text-')} border ${project.color}/30 bg-black/20`}>
                              {project.status}
                            </span>
                            <span className="text-gray-400 text-sm">{project.company}</span>
                          </div>
                          <h3 className="font-orbitron text-base xs:text-lg sm:text-xl lg:text-2xl font-bold text-gray-100 leading-tight">
                            {project.title}
                          </h3>
                        </div>
                      </div>
                      
                      <p className="text-gray-300 text-sm sm:text-base lg:text-lg leading-relaxed">
                        {project.description}
                      </p>
                    </div>

                    {/* Metrics */}
                    <div className="grid grid-cols-3 gap-2 sm:gap-4">
                      {project.metrics.map((metric, idx) => (
                        <div key={idx} className="text-center">
                          <div className={`font-orbitron text-sm sm:text-base lg:text-lg font-bold ${project.color.replace('border-', 'text-')}`}>
                            {metric.split(' ')[0]}
                          </div>
                          <div className="text-xs text-gray-400 font-fira">
                            {metric.split(' ').slice(1).join(' ')}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Technologies */}
                    <div className="space-y-3">
                      <h4 className="font-fira text-sm font-bold text-gray-400 uppercase tracking-wider">
                        Technologies Used
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech) => (
                          <span 
                            key={tech}
                            className={`px-3 py-1 bg-black/30 border ${project.color}/20 rounded-lg text-sm font-fira text-gray-300 hover:border-${project.color.split('-')[1]}-400/50 transition-colors`}
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Duration */}
                    <div className="flex items-center space-x-2 text-sm text-gray-400">
                      <Calendar size={16} />
                      <span className="font-fira">{project.duration}</span>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-4 pt-4">
                      <Button 
                        className={`cyber-button ${project.color.replace('border-', 'bg-')} hover:scale-105 transition-transform`}
                        size="sm"
                      >
                        <ExternalLink className="mr-2" size={16} />
                        VIEW PROJECT
                      </Button>
                      <Button 
                        variant="outline"
                        className={`border-2 ${project.color} ${project.color.replace('border-', 'text-')} hover:${project.color.replace('border-', 'bg-')} hover:text-black transition-all`}
                        size="sm"
                      >
                        <Github className="mr-2" size={16} />
                        SOURCE CODE
                      </Button>
                    </div>
                  </div>

                  {/* Visual Side - Project Image */}
                  <div className={`relative p-4 sm:p-6 lg:p-8 xl:p-12 bg-gradient-to-br ${project.bgColor} to-transparent`}>
                    <div className="h-full flex items-center justify-center">
                      {/* Project Image Container */}
                      <div className="relative w-full max-w-sm">
                        {/* Main Project Image */}
                        <div className={`w-full h-64 lg:h-80 rounded-2xl border-2 ${project.color}/50 overflow-hidden backdrop-blur-sm ${
                          isMobile ? 'transition-transform duration-300' : 'group-hover:scale-105 transition-transform duration-500'
                        }`}>
                          <img
                            src={project.image}
                            alt={project.title}
                            className="w-full h-full object-cover"
                            loading="lazy"
                            decoding="async"
                          />
                          {/* Image Overlay */}
                          <div className={`absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent`}></div>
                          
                          {/* Category Badge */}
                          <div className="absolute top-4 left-4">
                            <div className={`px-3 py-1 rounded-full text-xs font-fira ${project.color.replace('border-', 'text-')} border ${project.color}/30 bg-black/80 backdrop-blur-sm`}>
                              {project.category}
                            </div>
                          </div>
                          
                          {/* Status Badge */}
                          <div className="absolute top-4 right-4">
                            <div className={`px-2 py-1 rounded-full text-xs font-fira text-white bg-${project.color.replace('border-', '').replace('[hsl(191,100%,50%)]', 'cyan-500').replace('[hsl(261,100%,67%)]', 'purple-500').replace('[hsl(213,100%,50%)]', 'blue-500')}/80 backdrop-blur-sm`}>
                              {project.status}
                            </div>
                          </div>
                        </div>

                        {/* Floating Tech Elements - Reduced for mobile */}
                        {!isMobile && (
                          <>
                            <div className={`absolute -top-4 -right-4 w-12 h-12 border-2 ${project.color} rounded-lg animate-spin-slow opacity-60`}></div>
                            <div className={`absolute -bottom-4 -left-4 w-8 h-8 ${project.color.replace('border-', 'bg-')} rounded-full animate-bounce-slow opacity-70`}></div>
                          </>
                        )}

                        {/* Mobile-optimized elements */}
                        {isMobile && (
                          <>
                            <div className={`absolute -top-2 -right-2 w-6 h-6 border ${project.color} rounded opacity-40`}></div>
                            <div className={`absolute -bottom-2 -left-2 w-4 h-4 ${project.color.replace('border-', 'bg-')} rounded-full opacity-50`}></div>
                          </>
                        )}
                        
                        {/* Corner Accents */}
                        <div className={`absolute top-2 right-2 w-4 h-4 border-t-2 border-r-2 ${project.color} opacity-60`}></div>
                        <div className={`absolute bottom-2 left-2 w-4 h-4 border-b-2 border-l-2 ${project.color} opacity-60`}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* All Projects Grid */}
        <div className="text-center mb-8">
          <h3 className="font-orbitron text-xl xs:text-2xl sm:text-3xl lg:text-4xl font-black mb-4 tracking-wider">
            <span className="bg-gradient-to-r from-[#00D9FF] to-[#8B5FFF] bg-clip-text text-transparent">
              ALL PROJECTS
            </span>
          </h3>
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {projects.map((project) => {
            const IconComponent = project.icon;
            return (
              <div 
                key={project.id}
                className={`cyber-card p-4 sm:p-5 lg:p-6 rounded-xl border ${project.color}/30 hover:border-${project.color.split('-')[1]}-400/60 transition-all group`}
              >
                <div className="space-y-4">
                  {/* Project Image */}
                  <div className="relative h-32 sm:h-40 rounded-lg overflow-hidden border border-white/10">
                    <img 
                      src={project.image}
                      alt={project.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    {/* Image Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    
                    {/* Category Badge */}
                    <div className="absolute top-2 left-2">
                      <div className={`px-2 py-1 rounded text-xs font-fira ${project.color.replace('border-', 'text-')} border ${project.color}/30 bg-black/80 backdrop-blur-sm`}>
                        {project.category}
                      </div>
                    </div>
                    
                    {/* Action Icons */}
                    <div className="absolute top-2 right-2 flex space-x-1">
                      <Github className="text-white/80 hover:text-white cursor-pointer transition-colors" size={16} />
                      <ExternalLink className="text-white/80 hover:text-white cursor-pointer transition-colors" size={16} />
                    </div>
                  </div>
                  
                  {/* Project Header */}
                  <div className="flex items-start justify-between">
                    <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-lg bg-gradient-to-br ${project.bgColor} to-transparent border ${project.color}/30 flex items-center justify-center group-hover:scale-110 transition-transform`}>
                      <IconComponent className={project.color.replace('border-', 'text-')} size={18} />
                    </div>
                    <div className={`text-xs font-fira ${project.color.replace('border-', 'text-')}`}>
                      {project.status}
                    </div>
                  </div>

                  {/* Project Details */}
                  <div>
                    <h4 className="font-orbitron text-sm xs:text-base sm:text-lg font-bold text-gray-100 mb-2 leading-tight">
                      {project.title}
                    </h4>
                    <p className="text-sm text-gray-400 mb-3 line-clamp-2">
                      {project.description}
                    </p>
                    <div className={`text-xs ${project.color.replace('border-', 'text-')} font-fira`}>
                      {project.category}
                    </div>
                  </div>

                  {/* Technologies Preview */}
                  <div className="flex flex-wrap gap-1">
                    {project.technologies.slice(0, 3).map((tech) => (
                      <span 
                        key={tech}
                        className="px-2 py-1 bg-black/30 border border-gray-600/30 rounded text-xs font-fira text-gray-400"
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span className="px-2 py-1 text-xs font-fira text-gray-500">
                        +{project.technologies.length - 3}
                      </span>
                    )}
                  </div>

                  {/* Status */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-700/30">
                    <span className={`text-xs font-fira ${project.color.replace('border-', 'text-')}`}>
                      {project.status}
                    </span>
                    <div className="flex items-center space-x-1">
                      {project.featured && <Star className="text-yellow-400" size={12} />}
                      <TrendingUp className="text-gray-400" size={12} />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* View More */}
        <div className="text-center mt-12">
          <Button 
            variant="outline"
            size="lg"
            className="border-2 border-[hsl(191,100%,50%)] text-[hsl(191,100%,50%)] px-8 py-4 rounded-lg font-fira font-medium hover:bg-[hsl(191,100%,50%)] hover:text-black transition-all group"
          >
            <Github className="mr-2 group-hover:scale-110 transition-transform" size={20} />
            VIEW ALL ON GITHUB
          </Button>
        </div>
      </div>
    </section>
  );
}