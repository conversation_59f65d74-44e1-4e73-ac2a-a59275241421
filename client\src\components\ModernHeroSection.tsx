import { But<PERSON> } from "@/components/ui/button";
import { Github, Linkedin, Mail, Download, ArrowRight, Code, Zap } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import myPic from "@/assets/my_pic.jpg";

const scrollTo = (elementId: string) => {
  const element = document.getElementById(elementId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

export default function ModernHeroSection() {
  const isMobile = useIsMobile();

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden pt-16 sm:pt-20">
      {/* Content Container */}
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="grid lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-center">
          
          {/* Left Column - Text Content */}
          <div className="space-y-8 animate-fade-in">
            {/* Status Badge */}
            <div className="inline-flex items-center space-x-2 glass-effect px-4 py-2 rounded-full border border-[hsl(191,100%,50%)]/30">
              <div className="w-2 h-2 bg-[hsl(191,100%,50%)] rounded-full animate-pulse"></div>
              <span className="font-fira text-sm text-gray-300">Available for work</span>
            </div>

            {/* Main Title */}
            <div className="space-y-6">
              <h1 className="font-orbitron text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-black leading-tight">
                <span className="block text-gray-100">KRISH</span>
                <span className="block bg-gradient-to-r from-[#00D9FF] to-[#8B5FFF] bg-clip-text text-transparent">
                  YADAV
                </span>
              </h1>
              
              <div className="space-y-3">
                <p className="font-fira text-lg sm:text-xl md:text-2xl text-[hsl(191,100%,50%)] flex items-center space-x-2">
                  <Code className="text-[hsl(261,100%,67%)]" size={20} />
                  <span>&lt; Software Engineer / &gt;</span>
                </p>
                <p className="text-base sm:text-lg text-gray-300 max-w-xl leading-relaxed">
                  Building scalable software solutions with modern web technologies. 
                  Specializing in full-stack development and machine learning with proven track record.
                </p>
              </div>
            </div>

            {/* Stats Row */}
            <div className="grid grid-cols-3 gap-2 sm:gap-4 lg:gap-6">
              <div className="text-center cyber-card p-2 sm:p-4 rounded-xl">
                <div className="font-orbitron text-lg sm:text-2xl font-bold text-[hsl(191,100%,50%)]">20K+</div>
                <div className="font-fira text-xs sm:text-sm text-gray-400">Lines of Code</div>
              </div>
              <div className="text-center cyber-card p-2 sm:p-4 rounded-xl">
                <div className="font-orbitron text-lg sm:text-2xl font-bold text-[hsl(261,100%,67%)]">98%</div>
                <div className="font-fira text-xs sm:text-sm text-gray-400">ML Accuracy</div>
              </div>
              <div className="text-center cyber-card p-2 sm:p-4 rounded-xl">
                <div className="font-orbitron text-lg sm:text-2xl font-bold text-[hsl(213,100%,50%)]">5+</div>
                <div className="font-fira text-xs sm:text-sm text-gray-400">Projects</div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                size="lg"
                className="cyber-button px-8 py-4 rounded-lg font-fira font-medium text-lg group"
                onClick={() => scrollTo('projects')}
              >
                <Zap className="mr-2" size={20} />
                VIEW PROJECTS
                <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={20} />
              </Button>
              <Button 
                variant="outline"
                size="lg"
                className="border-2 border-[hsl(191,100%,50%)] text-[hsl(191,100%,50%)] px-8 py-4 rounded-lg font-fira font-medium text-lg hover:bg-[hsl(191,100%,50%)] hover:text-black transition-all group"
              >
                <Download className="mr-2" size={20} />
                DOWNLOAD CV
              </Button>
            </div>

            {/* Social Links */}
            <div className="flex space-x-6 pt-4">
              <a href="#" className="p-3 glass-effect rounded-full border border-[hsl(191,100%,50%)]/30 hover:border-[hsl(191,100%,50%)] transition-all group">
                <Github className="text-[hsl(191,100%,50%)] group-hover:scale-110 transition-transform" size={24} />
              </a>
              <a href="#" className="p-3 glass-effect rounded-full border border-[hsl(261,100%,67%)]/30 hover:border-[hsl(261,100%,67%)] transition-all group">
                <Linkedin className="text-[hsl(261,100%,67%)] group-hover:scale-110 transition-transform" size={24} />
              </a>
              <a href="mailto:<EMAIL>" className="p-3 glass-effect rounded-full border border-[hsl(213,100%,50%)]/30 hover:border-[hsl(213,100%,50%)] transition-all group">
                <Mail className="text-[hsl(213,100%,50%)] group-hover:scale-110 transition-transform" size={24} />
              </a>
            </div>
          </div>

          {/* Right Column - Profile Visual */}
          <div className="relative h-64 sm:h-80 lg:h-96 xl:h-[500px] animate-slide-up">
            {/* Main Profile Container */}
            <div className="relative w-full h-full mx-auto">
              {/* Animated Border Rings */}
              <div className="absolute inset-8 rounded-full border-2 border-[hsl(191,100%,50%)]/30 animate-spin-slow"></div>
              <div className="absolute inset-12 rounded-full border border-[hsl(191,100%,50%)]/20 animate-pulse"></div>
              
              {/* Profile Picture Container */}
              <div className="absolute inset-16 rounded-full bg-gradient-to-br from-[hsl(191,100%,50%)]/20 via-[hsl(240,100%,50%)]/10 to-[hsl(280,100%,50%)]/20 backdrop-blur-sm border border-white/10 overflow-hidden">
                {/* Profile Image */}
                <div className="w-full h-full rounded-full overflow-hidden border-4 border-[hsl(191,100%,50%)]/30">
                  <img
                    src={myPic}
                    alt="Krish Yadav - Software Engineer"
                    className={`w-full h-full object-cover object-center ${
                      isMobile
                        ? 'transition-transform duration-300'
                        : 'hover:scale-110 transition-transform duration-500'
                    }`}
                    loading="eager"
                    decoding="async"
                  />
                </div>
                
                {/* Status Indicator */}
                <div className="absolute right-8 bottom-8 transform -translate-x-20 bg-black/80 backdrop-blur-sm rounded-full px-3 py-1 border border-[hsl(191,100%,50%)]/30">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-[hsl(120,100%,50%)] rounded-full animate-pulse"></div>
                    <span className="font-fira text-xs text-[hsl(191,100%,50%)]">ONLINE</span>
                  </div>
                </div>
              </div>
              
              {/* Floating Tech Elements - Reduced for mobile */}
              {!isMobile && (
                <>
                  <div className="absolute left-4 top-8 w-8 h-8 border-2 border-[hsl(191,100%,50%)] rounded-lg animate-spin-slow opacity-60"></div>
                  <div className="absolute right-8 bottom-4 w-6 h-6 bg-[hsl(191,100%,50%)] rounded-full animate-bounce-slow opacity-70"></div>
                  <div className="absolute top-2 left-1/2 w-4 h-4 border border-[hsl(240,100%,50%)] rounded-full animate-pulse opacity-50"></div>
                  <div className="absolute top-1/4 right-2 w-3 h-3 bg-[hsl(280,100%,50%)] rounded-full animate-ping opacity-60"></div>
                </>
              )}

              {/* Mobile-optimized elements */}
              {isMobile && (
                <>
                  <div className="absolute left-2 top-4 w-4 h-4 border border-[hsl(191,100%,50%)] rounded opacity-40"></div>
                  <div className="absolute right-4 bottom-2 w-3 h-3 bg-[hsl(191,100%,50%)] rounded-full opacity-50"></div>
                </>
              )}
              
              {/* Code Accent Lines */}
              <div className="absolute left-8 top-20 right-20 space-y-2 flex flex-row">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div 
                    key={i}
                    className="h-1 bg-gradient-to-r from-[hsl(191,100%,50%)]/40 to-transparent rounded animate-fade-in"
                    style={{ 
                      animationDelay: `${i * 0.3}s`,
                      width: `${60 + i * 15}%`
                    }}
                  ></div>
                ))}
              </div>
              
              <div className="absolute right-8 bottom-20 left-20 space-y-2 flex flex-row">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div 
                    key={i}
                    className="h-1 bg-gradient-to-r from-transparent to-[hsl(261,100%,67%)]/40 rounded animate-fade-in"
                    style={{ 
                      animationDelay: `${i * 0.3 + 1}s`,
                      width: `${50 + i * 20}%`
                    }}
                  ></div>
                ))}
              </div>
            </div>
            
            {/* Background Glow - Simplified for mobile */}
            <div className={`absolute inset-0 bg-gradient-radial from-[hsl(191,100%,50%)]/20 via-transparent to-transparent ${
              isMobile ? 'blur-xl' : 'blur-3xl animate-pulse'
            }`}></div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-[hsl(191,100%,50%)] rounded-full p-1">
          <div className="w-1 h-3 bg-[hsl(191,100%,50%)] rounded-full animate-pulse"></div>
        </div>
      </div>
    </section>
  );
}