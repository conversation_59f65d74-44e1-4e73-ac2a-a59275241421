import { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

interface LoadingScreenProps {
  onLoadingComplete: () => void;
}

export default function LoadingScreen({ onLoadingComplete }: LoadingScreenProps) {
  const [isVisible, setIsVisible] = useState(true);
  const isMobile = useIsMobile();

  useEffect(() => {
    // Faster loading on mobile for better UX
    const loadingTime = isMobile ? 1000 : 2000;

    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onLoadingComplete, 300); // Faster transition
    }, loadingTime);

    return () => clearTimeout(timer);
  }, [onLoadingComplete, isMobile]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-[hsl(220,30%,8%)] z-50 flex items-center justify-center transition-opacity duration-500">
      <div className="text-center">
        <div className="loading-spinner mx-auto mb-4"></div>
        <p className="text-[hsl(191,100%,50%)] font-fira">Initializing...</p>
      </div>
    </div>
  );
}
