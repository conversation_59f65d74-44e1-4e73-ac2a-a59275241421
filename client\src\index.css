@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Fira+Code:wght@300;400;500&family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(220, 30%, 8%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(220, 30%, 15%);
  --muted-foreground: hsl(215, 20%, 65%);
  --popover: hsl(220, 30%, 8%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(220, 30%, 12%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(217, 32%, 17%);
  --input: hsl(217, 32%, 17%);
  --primary: hsl(191, 100%, 50%);
  --primary-foreground: hsl(220, 30%, 8%);
  --secondary: hsl(217, 32%, 17%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(217, 32%, 17%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(191, 100%, 50%);
  --radius: 0.5rem;
  
  /* Custom navy cyberpunk colors */
  --cyber-navy: hsl(220, 30%, 8%);
  --cyber-dark-blue: hsl(220, 30%, 12%);
  --cyber-cyan: hsl(191, 100%, 50%);
  --cyber-purple: hsl(261, 100%, 67%);
  --cyber-blue: hsl(213, 100%, 50%);
  --cyber-gray: hsl(215, 25%, 27%);
  --cyber-light-text: hsl(210, 40%, 98%);
}

.dark {
  --background: hsl(212, 72%, 9%);
  --foreground: hsl(210, 17%, 95%);
  --muted: hsl(217, 32%, 17%);
  --muted-foreground: hsl(215, 20%, 65%);
  --popover: hsl(212, 72%, 9%);
  --popover-foreground: hsl(210, 17%, 95%);
  --card: hsl(212, 72%, 9%);
  --card-foreground: hsl(210, 17%, 95%);
  --border: hsl(217, 32%, 17%);
  --input: hsl(217, 32%, 17%);
  --primary: hsl(191, 100%, 50%);
  --primary-foreground: hsl(212, 72%, 9%);
  --secondary: hsl(217, 32%, 17%);
  --secondary-foreground: hsl(210, 17%, 95%);
  --accent: hsl(217, 32%, 17%);
  --accent-foreground: hsl(210, 17%, 95%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 17%, 95%);
  --ring: hsl(191, 100%, 50%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    min-height: 100vh;
    overflow-x: hidden;
  }
}

@layer utilities {
  .font-orbitron {
    font-family: 'Orbitron', monospace;
  }
  
  .font-fira {
    font-family: 'Fira Code', monospace;
  }
  
  .gradient-bg {
    background: transparent;
    position: relative;
  }
  
  .neon-text {
    color: var(--cyber-cyan);
    font-weight: 900;
    text-shadow: 0 0 10px var(--cyber-cyan), 0 0 20px var(--cyber-cyan), 0 0 30px var(--cyber-cyan);
  }
  
  .cyber-border {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple)) 1;
  }
  
  .glass-effect {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 217, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 217, 255, 0.15);
  }
  
  .cyber-card {
    background: linear-gradient(145deg, rgba(0, 217, 255, 0.1), rgba(139, 95, 255, 0.1));
    border: 1px solid rgba(0, 217, 255, 0.3);
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 217, 255, 0.1);
  }
  
  .cyber-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 217, 255, 0.2);
    border-color: var(--cyber-cyan);
    background: linear-gradient(145deg, rgba(0, 217, 255, 0.2), rgba(139, 95, 255, 0.2));
  }
  
  .cyber-button {
    position: relative;
    background: linear-gradient(45deg, var(--cyber-cyan), var(--cyber-purple));
    border: none;
    color: white;
    transition: all 0.3s ease;
    overflow: hidden;
  }
  
  .cyber-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }
  
  .cyber-button:hover::before {
    left: 100%;
  }
  
  .skill-bar {
    background: linear-gradient(90deg, var(--cyber-cyan), var(--cyber-purple));
    height: 6px;
    border-radius: 3px;
    position: relative;
    overflow: hidden;
  }
  
  .skill-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
  }
  
  .section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--cyber-cyan), transparent);
    margin: 4rem 0;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  .animate-spin-slow {
    animation: spin 20s linear infinite;
  }

  /* Mobile performance optimizations */
  @media (max-width: 768px) {
    .animate-float-3d {
      animation: float 4s ease-in-out infinite; /* Fallback to simple float on mobile */
    }

    .animate-spin-slow {
      animation: spin 30s linear infinite; /* Slower animation on mobile */
    }

    .animate-bounce-slow {
      animation: bounce-slow 6s ease-in-out infinite; /* Slower bounce on mobile */
    }

    /* Disable complex animations on mobile */
    .animate-glow {
      animation: none;
      box-shadow: 0 0 10px var(--cyber-cyan);
    }

    /* Optimize transforms for mobile */
    * {
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
      -webkit-perspective: 1000;
      perspective: 1000;
    }

    /* Reduce blur effects on mobile */
    .blur-3xl {
      filter: blur(8px);
    }

    /* Simplify gradients on mobile */
    .bg-gradient-radial {
      background: radial-gradient(circle, var(--tw-gradient-stops));
    }

    /* Optimize glass effects */
    .glass-effect {
      backdrop-filter: blur(4px);
      -webkit-backdrop-filter: blur(4px);
    }
  }

  /* Performance optimizations for all devices */
  .animate-float-3d,
  .animate-spin-slow,
  .animate-bounce-slow {
    will-change: transform;
  }

  img {
    will-change: auto;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  from { box-shadow: 0 0 20px var(--cyber-cyan); }
  to { box-shadow: 0 0 30px var(--cyber-cyan), 0 0 40px var(--cyber-cyan); }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes float-3d {
  0% { 
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px) translateY(0px);
  }
  25% { 
    transform: perspective(1000px) rotateX(90deg) rotateY(90deg) translateZ(50px) translateY(-30px);
  }
  50% { 
    transform: perspective(1000px) rotateX(180deg) rotateY(180deg) translateZ(0px) translateY(-50px);
  }
  75% { 
    transform: perspective(1000px) rotateX(270deg) rotateY(270deg) translateZ(-50px) translateY(-30px);
  }
  100% { 
    transform: perspective(1000px) rotateX(360deg) rotateY(360deg) translateZ(0px) translateY(0px);
  }
}

@keyframes spin-slow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bounce-slow {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float-3d {
  animation: float-3d 8s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-bounce-slow {
  animation: bounce-slow 4s ease-in-out infinite;
}

.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes shimmer {
  100% { left: 100%; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Loading spinner */
.loading-spinner {
  border: 2px solid rgba(0, 217, 255, 0.2);
  border-top: 2px solid var(--cyber-cyan);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}
