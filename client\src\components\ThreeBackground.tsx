import { useEffect, useState, useMemo } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  speed: number;
  color: string;
  opacity: number;
}

export default function ThreeBackground() {
  const [particles, setParticles] = useState<Particle[]>([]);
  const isMobile = useIsMobile();

  // Reduce particle count and complexity on mobile
  const particleConfig = useMemo(() => {
    if (isMobile) {
      return {
        count: 30, // Reduced from 120
        maxSize: 4, // Reduced from 8
        enableComplexAnimations: false
      };
    }
    return {
      count: 80, // Reduced from 120 even on desktop
      maxSize: 6, // Slightly reduced
      enableComplexAnimations: true
    };
  }, [isMobile]);

  useEffect(() => {
    const generateParticles = () => {
      const newParticles: Particle[] = [];
      const colors = ['#00D9FF', '#8B5FFF', '#00FF9F', '#FF6B9D', '#FFD700'];

      for (let i = 0; i < particleConfig.count; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * particleConfig.maxSize + 2,
          speed: Math.random() * 4 + 1,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.7 + 0.2 // Slightly more opaque for better visibility with fewer particles
        });
      }
      setParticles(newParticles);
    };

    generateParticles();
  }, [particleConfig]);

  return (
    <div className="fixed inset-0 z-[-1] pointer-events-none overflow-hidden">
      {/* Deep space background */}
      <div className="absolute inset-0 bg-black">
        <div className="absolute inset-0 bg-gradient-radial from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] animate-pulse" style={{ animationDuration: '12s' }} />
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-[#0D1B2A]/20 to-[#1B263B]/30" />
      </div>
      
      {/* Floating particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          className={`absolute rounded-full ${
            particleConfig.enableComplexAnimations
              ? 'animate-float-3d'
              : 'animate-float' // Simple 2D animation for mobile
          }`}
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            opacity: particle.opacity,
            // Simplified shadows for mobile
            boxShadow: isMobile
              ? `0 0 ${particle.size * 2}px ${particle.color}60`
              : `
                0 0 ${particle.size * 4}px ${particle.color},
                0 0 ${particle.size * 8}px ${particle.color}40,
                inset 0 0 ${particle.size}px ${particle.color}80
              `,
            // Simplified transforms for mobile
            transform: particleConfig.enableComplexAnimations
              ? `perspective(1000px) rotateX(${particle.id * 15}deg) rotateY(${particle.id * 10}deg)`
              : 'none',
            animationDelay: `${particle.id * 0.1}s`,
            animationDuration: `${6 + particle.speed}s`,
            // Use will-change for better performance
            willChange: 'transform'
          }}
        />
      ))}
      
      {/* Cyberpunk grid overlay - Simplified for mobile */}
      {!isMobile && (
        <div
          className="absolute inset-0 opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 217, 255, 0.5) 2px, transparent 2px),
              linear-gradient(90deg, rgba(0, 217, 255, 0.5) 2px, transparent 2px),
              linear-gradient(rgba(139, 95, 255, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(139, 95, 255, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '100px 100px, 100px 100px, 20px 20px, 20px 20px'
          }}
        />
      )}

      {/* Mobile-optimized simple grid */}
      {isMobile && (
        <div
          className="absolute inset-0 opacity-3"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 217, 255, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 217, 255, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      )}

      {/* Floating geometric shapes - Reduced for mobile */}
      {!isMobile && (
        <>
          <div className="absolute top-20 left-20 w-16 h-16 border-2 border-[#00D9FF] opacity-20 animate-spin-slow"></div>
          <div className="absolute top-40 right-32 w-12 h-12 border-2 border-[#8B5FFF] opacity-15 animate-pulse transform rotate-45"></div>
          <div className="absolute bottom-32 left-40 w-20 h-20 border border-[#00FF9F] opacity-10 animate-bounce-slow rounded-full"></div>
        </>
      )}

      {/* Mobile-optimized shapes */}
      {isMobile && (
        <>
          <div className="absolute top-20 left-10 w-8 h-8 border border-[#00D9FF] opacity-15 animate-pulse"></div>
          <div className="absolute bottom-32 right-10 w-6 h-6 border border-[#8B5FFF] opacity-10 rounded-full"></div>
        </>
      )}
    </div>
  );
}
