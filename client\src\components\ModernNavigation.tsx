import { useState, useEffect } from 'react';
import { Menu, X, Code2, User, Briefcase, Mail } from 'lucide-react';
import { Button } from "@/components/ui/button";

const scrollTo = (elementId: string) => {
  const element = document.getElementById(elementId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

const navigation = [
  { id: 'home', label: 'HOME', icon: Code2 },
  { id: 'about', label: 'ABOUT', icon: User },
  { id: 'skills', label: 'SKILLS', icon: Code2 },
  { id: 'projects', label: 'PROJECTS', icon: Briefcase },
  { id: 'contact', label: 'CONTACT', icon: Mail },
];

export default function ModernNavigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
      
      // Update active section based on scroll position
      const sections = navigation.map(nav => nav.id);
      const current = sections.find(section => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      
      if (current) {
        setActiveSection(current);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (id: string) => {
    scrollTo(id);
    setIsOpen(false);
    setActiveSection(id);
  };

  return (
    <nav 
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        scrolled 
          ? 'glass-effect border-b border-[hsl(191,100%,50%)]/20' 
          : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div 
            className="font-orbitron text-lg sm:text-xl lg:text-2xl font-bold neon-text cursor-pointer flex items-center space-x-2 group" 
            onClick={() => handleNavClick('home')}
          >
            <div className="w-6 h-6 sm:w-8 sm:h-8 border-2 border-[hsl(191,100%,50%)] rounded rotate-45 group-hover:rotate-90 transition-transform duration-300"></div>
            <span className="hidden sm:block">KRISH YADAV</span>
            <span className="sm:hidden">KRISH YADAV</span>
          </div>
          
          {/* Desktop Menu */}
          <div className="hidden md:flex items-center space-x-1">
            {navigation.map((item) => {
              const IconComponent = item.icon;
              return (
                <Button
                  key={item.id}
                  variant="ghost"
                  className={`relative font-fira px-3 sm:px-4 lg:px-6 py-2 sm:py-3 rounded-lg transition-all duration-300 group text-sm sm:text-base ${
                    activeSection === item.id
                      ? 'bg-[hsl(191,100%,50%)]/20 text-[hsl(191,100%,50%)] border border-[hsl(191,100%,50%)]/50'
                      : 'text-gray-300 hover:text-[hsl(191,100%,50%)] hover:bg-[hsl(191,100%,50%)]/10'
                  }`}
                  onClick={() => handleNavClick(item.id)}
                >
                  <IconComponent className="mr-1 sm:mr-2 group-hover:scale-110 transition-transform" size={16} />
                  <span className="hidden lg:inline">{item.label}</span>
                  <span className="lg:hidden">{item.label.substring(0, 4)}</span>
                  {activeSection === item.id && (
                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-[hsl(191,100%,50%)] rounded-full"></div>
                  )}
                </Button>
              );
            })}
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden text-[hsl(191,100%,50%)] hover:bg-[hsl(191,100%,50%)]/20 p-2"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X size={20} /> : <Menu size={20} />}
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      <div 
        className={`md:hidden fixed inset-0 bg-black/95 backdrop-blur-lg transition-all duration-300 z-40 ${
          isOpen ? 'opacity-100 visible' : 'opacity-0 invisible'
        }`}
        style={{ top: scrolled ? '56px' : '64px' }}
      >
        <div className="container mx-auto px-4 py-6">
          <div className="space-y-3">
            {navigation.map((item) => {
              const IconComponent = item.icon;
              return (
                <Button
                  key={item.id}
                  variant="ghost"
                  className={`w-full justify-start font-fira text-base py-4 px-4 rounded-lg transition-all duration-300 group ${
                    activeSection === item.id
                      ? 'bg-[hsl(191,100%,50%)]/20 text-[hsl(191,100%,50%)] border border-[hsl(191,100%,50%)]/50'
                      : 'text-gray-300 hover:text-[hsl(191,100%,50%)] hover:bg-[hsl(191,100%,50%)]/10'
                  }`}
                  onClick={() => handleNavClick(item.id)}
                >
                  <IconComponent className="mr-3 group-hover:scale-110 transition-transform" size={18} />
                  {item.label}
                  {activeSection === item.id && (
                    <div className="ml-auto w-2 h-2 bg-[hsl(191,100%,50%)] rounded-full animate-pulse"></div>
                  )}
                </Button>
              );
            })}
          </div>
          
          {/* Mobile Menu Footer */}
          <div className="mt-8 pt-6 border-t border-gray-700/50">
            <div className="text-center">
              <p className="font-fira text-sm text-gray-400">KRISH YADAV</p>
              <p className="font-fira text-xs text-gray-500 mt-1">Software Engineer</p>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}