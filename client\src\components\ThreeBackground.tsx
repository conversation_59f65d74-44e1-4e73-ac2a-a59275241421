import { useEffect, useState, useMemo } from "react";
import { useIsMobile } from "@/hooks/use-mobile";

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  speed: number;
  color: string;
  opacity: number;
}

export default function ThreeBackground() {
  const [particles, setParticles] = useState<Particle[]>([]);
  const isMobile = useIsMobile();

  // Balanced particle count for mobile and desktop
  const particleConfig = useMemo(() => {
    if (isMobile) {
      return {
        count: 60, // Increased from 30 to 60 for better visual effect
        maxSize: 5, // Increased from 4 to 5
        enableComplexAnimations: false // Keep simple animations for performance
      };
    }
    return {
      count: 100, // Increased from 80 to 100 for richer desktop experience
      maxSize: 8, // Back to original size
      enableComplexAnimations: true
    };
  }, [isMobile]);

  useEffect(() => {
    const generateParticles = () => {
      const newParticles: Particle[] = [];
      const colors = ['#00D9FF', '#8B5FFF', '#00FF9F', '#FF6B9D', '#FFD700'];

      for (let i = 0; i < particleConfig.count; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * particleConfig.maxSize + 2,
          speed: Math.random() * 4 + 1,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: isMobile
            ? Math.random() * 0.8 + 0.3 // More opaque on mobile for better visibility
            : Math.random() * 0.7 + 0.2 // Standard opacity on desktop
        });
      }
      setParticles(newParticles);
    };

    generateParticles();
  }, [particleConfig]);

  return (
    <div className="fixed inset-0 z-[-1] pointer-events-none overflow-hidden">
      {/* Deep space background */}
      <div className="absolute inset-0 bg-black">
        <div className="absolute inset-0 bg-gradient-radial from-[#0a0a0a] via-[#1a1a2e] to-[#16213e] animate-pulse" style={{ animationDuration: '12s' }} />
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-[#0D1B2A]/20 to-[#1B263B]/30" />
      </div>
      
      {/* Floating particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          className={`absolute rounded-full ${
            particleConfig.enableComplexAnimations
              ? 'animate-float-3d'
              : 'animate-float' // Simple 2D animation for mobile
          }`}
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            opacity: particle.opacity,
            // Enhanced shadows for mobile while keeping performance
            boxShadow: isMobile
              ? `
                0 0 ${particle.size * 3}px ${particle.color}80,
                0 0 ${particle.size * 6}px ${particle.color}40
              `
              : `
                0 0 ${particle.size * 4}px ${particle.color},
                0 0 ${particle.size * 8}px ${particle.color}40,
                inset 0 0 ${particle.size}px ${particle.color}80
              `,
            // Simplified transforms for mobile
            transform: particleConfig.enableComplexAnimations
              ? `perspective(1000px) rotateX(${particle.id * 15}deg) rotateY(${particle.id * 10}deg)`
              : 'none',
            animationDelay: `${particle.id * 0.1}s`,
            animationDuration: `${6 + particle.speed}s`,
            // Use will-change for better performance
            willChange: 'transform'
          }}
        />
      ))}
      
      {/* Grid overlay removed - No grid on mobile or desktop */}

      {/* Floating geometric shapes - Reduced for mobile */}
      {!isMobile && (
        <>
          <div className="absolute top-20 left-20 w-16 h-16 border-2 border-[#00D9FF] opacity-20 animate-spin-slow"></div>
          <div className="absolute top-40 right-32 w-12 h-12 border-2 border-[#8B5FFF] opacity-15 animate-pulse transform rotate-45"></div>
          <div className="absolute bottom-32 left-40 w-20 h-20 border border-[#00FF9F] opacity-10 animate-bounce-slow rounded-full"></div>
        </>
      )}

      {/* Mobile-optimized shapes */}
      {isMobile && (
        <>
          <div className="absolute top-20 left-10 w-8 h-8 border border-[#00D9FF] opacity-15 animate-pulse"></div>
          <div className="absolute bottom-32 right-10 w-6 h-6 border border-[#8B5FFF] opacity-10 rounded-full"></div>
        </>
      )}
    </div>
  );
}
