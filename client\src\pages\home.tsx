import { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { usePerformance } from "@/hooks/use-performance";
import ModernNavigation from "@/components/ModernNavigation";
import LoadingScreen from "@/components/LoadingScreen";
import ThreeBackground from "@/components/ThreeBackground";
import ModernHeroSection from "@/components/ModernHeroSection";
import ModernAboutSection from "@/components/ModernAboutSection";
import ModernSkillsSection from "@/components/ModernSkillsSection";
import ModernProjectsSection from "@/components/ModernProjectsSection";
import ModernContactSection from "@/components/ModernContactSection";

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);
  const [lowPerformanceMode, setLowPerformanceMode] = useState(false);
  const isMobile = useIsMobile();
  const { isLowPerformance } = usePerformance();

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  // Enable low performance mode if device is struggling
  useEffect(() => {
    if (isMobile && isLowPerformance) {
      setLowPerformanceMode(true);
      console.log('🚀 Low performance mode enabled for better mobile experience');
    }
  }, [isMobile, isLowPerformance]);

  return (
    <div className="min-h-screen gradient-bg text-gray-100">
      {isLoading && <LoadingScreen onLoadingComplete={handleLoadingComplete} />}

      {/* Conditionally render ThreeBackground based on performance */}
      {!lowPerformanceMode && <ThreeBackground />}

      {/* Fallback simple background for low performance mode */}
      {lowPerformanceMode && (
        <div className="fixed inset-0 z-[-1] pointer-events-none overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-[#0a0a0a] via-[#1a1a2e] to-[#16213e]" />
          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-[#0D1B2A]/20 to-[#1B263B]/30" />
        </div>
      )}

      <ModernNavigation />
      
      <main>
        <ModernHeroSection />
        <ModernAboutSection />
        <ModernSkillsSection />
        <ModernProjectsSection />
        <ModernContactSection />
      </main>
      
      {/* Card-Style Footer */}
      <footer className="pb-8 sm:pb-12 mt-0 sm:mt-0">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="glass-effect rounded-2xl border border-[hsl(191,100%,50%)]/30 p-4 sm:p-6 lg:p-8">

            {/* Mobile Layout */}
            {isMobile ? (
              <div className="space-y-6">
                {/* Brand Section */}
                <div className="text-center">
                  <div className="font-orbitron text-xl font-bold mb-2">KRISH YADAV</div>
                  <p className="text-gray-400 font-fira text-sm">Software Engineer</p>
                </div>

                {/* Info Grid */}
                <div className="grid grid-cols-2 gap-4">
                  {/* Tech Stack */}
                  <div className="text-center">
                    <p className="font-fira text-xs text-gray-400 uppercase tracking-wider mb-2">Tech Stack</p>
                    <div className="space-y-1">
                      <p className="text-gray-300 font-fira text-sm">React</p>
                      <p className="text-gray-300 font-fira text-sm">Node.js</p>
                      <p className="text-gray-300 font-fira text-sm">Python</p>
                    </div>
                  </div>

                  {/* Contact */}
                  <div className="text-center">
                    <p className="font-fira text-xs text-gray-400 uppercase tracking-wider mb-2">Contact</p>
                    <div className="space-y-1">
                      <p className="text-gray-300 font-fira text-xs">krishyadav454</p>
                      <p className="text-gray-300 font-fira text-xs">@gmail.com</p>
                      <p className="text-gray-300 font-fira text-xs">Lucknow, UP</p>
                    </div>
                  </div>
                </div>

                {/* Copyright */}
                <div className="text-center pt-4 border-t border-gray-700/30">
                  <p className="font-fira text-xs text-gray-500">© {new Date().getFullYear()} Krish Yadav • All rights reserved</p>
                </div>
              </div>
            ) : (
              /* Desktop Layout - Unchanged */
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 items-center">
                {/* Brand */}
                <div className="col-span-2 md:col-span-1">
                  <div className="font-orbitron text-lg sm:text-xl font-bold ">KRISH  YADAV</div>
                  <p className="text-gray-400 font-fira text-xs mt-1">Software Engineer</p>
                </div>

                {/* Tech Stack */}
                <div className="col-span-1 md:col-span-1">
                  <p className="font-fira text-xs text-gray-400 uppercase tracking-wider mb-2">Stack</p>
                  <p className="text-gray-300 font-fira text-xs sm:text-sm">React • Node • Python</p>
                </div>

                {/* Contact */}
                <div className="col-span-1 md:col-span-1">
                  <p className="font-fira text-xs text-gray-400 uppercase tracking-wider mb-2">Contact</p>
                  <p className="text-gray-300 font-fira text-xs sm:text-sm break-all"><EMAIL></p>
                </div>

                {/* Copyright */}
                <div className="col-span-2 md:col-span-1 text-left md:text-right">
                  <p className="font-fira text-xs text-gray-500">© {new Date().getFullYear()} Krish Yadav</p>
                  <p className="font-fira text-xs text-gray-500">All rights reserved</p>
                </div>
              </div>
            )}

          </div>
        </div>
      </footer>
    </div>
  );
}
