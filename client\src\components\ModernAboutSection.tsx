import { Building2, GraduationCap, Trophy, MapPin, Calendar, ExternalLink } from 'lucide-react';
import { useIsMobile } from "@/hooks/use-mobile";
import myPic from "@/assets/my_pic.jpg";

export default function ModernAboutSection() {
  const isMobile = useIsMobile();

  return (
    <section id="about" className="py-12 sm:py-20 relative">
      <div className="container mx-auto px-4 sm:px-6">


        <div className="text-center mb-8 sm:mb-16">
          <h2 className="font-orbitron text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-black mb-4 sm:mb-6 tracking-wider">
            <span className="bg-gradient-to-r from-[#00D9FF] via-[#8B5FFF] to-[#00FF9F] bg-clip-text text-transparent">
              ABOUT
            </span>
            <span className="text-white/90 ml-2">_ME</span>
          </h2>
          <p className="text-base sm:text-lg md:text-xl text-gray-300 max-w-2xl mx-auto">
            Passionate software engineer creating innovative solutions with modern technologies
          </p>
        </div>

        {/* Mobile-only Profile Card - positioned after title */}
        {isMobile && (
          <div className="mb-8 mx-auto max-w-sm">
            <div className="glass-effect p-6 rounded-2xl border border-[hsl(191,100%,50%)]/30 text-center">
              <div className="w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-[hsl(191,100%,50%)] to-[hsl(261,100%,67%)] p-1">
                <div className="w-full h-full rounded-full overflow-hidden border border-white/10">
                  <img
                    src={myPic}
                    alt="Krish Yadav - Software Engineer"
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  />
                </div>
              </div>
              <h3 className="font-orbitron text-xl font-bold mb-2">Krish Yadav</h3>
              <p className="text-[hsl(191,100%,50%)] font-fira mb-4">Software Engineer</p>
              <div className="flex items-center justify-center text-sm text-gray-400">
                <MapPin className="mr-1" size={14} />
                Lucknow, Uttar Pradesh
              </div>
            </div>
          </div>
        )}

        <div className="grid lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-16">
          {/* Main Info Card */}
          <div className="lg:col-span-2 space-y-8">
            <div className="glass-effect p-4 sm:p-6 lg:p-8 rounded-2xl border border-[hsl(191,100%,50%)]/30">
              <h3 className="font-orbitron text-lg sm:text-xl lg:text-2xl font-bold text-[hsl(191,100%,50%)] mb-4 sm:mb-6">
                SOFTWARE ENGINEER
              </h3>
              <div className="space-y-4 sm:space-y-6 text-sm sm:text-base lg:text-lg text-gray-300 leading-relaxed">
                <p>
                  I'm a passionate Software Engineer with expertise in full-stack development and machine learning. 
                  Currently working at <span className="text-[hsl(191,100%,50%)] font-semibold">Digital Creative International</span>, 
                  where I build scalable platforms and workflow management systems from the ground up.
                </p>
                <p>
                  My journey in technology began at Babu Banarasi Das Institute of Technology, where I earned my 
                  B.Tech in Computer Science Engineering. I specialize in React.js, Node.js, Python, and have 
                  published research on CNN-based medical image analysis achieving 98% accuracy.
                </p>
                <p>
                  I believe in writing clean, efficient code and creating user-centric solutions that make a real impact. 
                  My experience spans across modern web technologies, database systems, and machine learning frameworks.
                </p>
              </div>
            </div>

            {/* Experience & Education Timeline */}
            <div className="grid md:grid-cols-2 gap-4 sm:gap-6">
              {/* Current Role */}
              <div className="cyber-card p-6 rounded-xl border border-[hsl(261,100%,67%)]/30">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-[hsl(261,100%,67%)] to-[hsl(191,100%,50%)] rounded-lg flex items-center justify-center">
                    <Building2 className="text-white" size={24} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-orbitron text-lg font-bold text-[hsl(261,100%,67%)] mb-2">
                      Software Engineer Intern
                    </h4>
                    <p className="text-gray-300 font-medium mb-1">Digital Creative International</p>
                    <div className="flex items-center text-sm text-gray-400 mb-3">
                      <Calendar className="mr-1" size={14} />
                      December 2024 - Present
                    </div>
                    <p className="text-sm text-gray-400">
                      Building workflow management systems with 20K+ lines of code across frontend, backend, and database systems.
                    </p>
                  </div>
                </div>
              </div>

              {/* Education */}
              <div className="cyber-card p-6 rounded-xl border border-[hsl(213,100%,50%)]/30">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-[hsl(213,100%,50%)] to-[hsl(191,100%,50%)] rounded-lg flex items-center justify-center">
                    <GraduationCap className="text-white" size={24} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-orbitron text-lg font-bold text-[hsl(213,100%,50%)] mb-2">
                      B.Tech Computer Science
                    </h4>
                    <p className="text-gray-300 font-medium mb-1">BBDIT&M, Lucknow</p>
                    <div className="flex items-center text-sm text-gray-400 mb-3">
                      <Calendar className="mr-1" size={14} />
                      2020 - 2024 • CGPA: 7.8
                    </div>
                    <p className="text-sm text-gray-400">
                      Specialized in algorithms, data structures, and software engineering principles.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Stats & Achievements */}
          <div className="space-y-6">
            {/* Profile Card - Hidden on mobile */}
            <div className={`glass-effect p-6 rounded-2xl border border-[hsl(191,100%,50%)]/30 text-center ${
              isMobile ? 'hidden' : 'block'
            }`}>
              <div className="w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-[hsl(191,100%,50%)] to-[hsl(261,100%,67%)] p-1">
                <div className="w-full h-full rounded-full overflow-hidden border border-white/10">
                  <img 
                    src={myPic}
                    alt="Krish Yadav - Software Engineer"
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  />
                </div>
              </div>
              <h3 className="font-orbitron text-xl font-bold mb-2">Krish Yadav</h3>
              <p className="text-[hsl(191,100%,50%)] font-fira mb-4">Software Engineer</p>
              <div className="flex items-center justify-center text-sm text-gray-400">
                <MapPin className="mr-1" size={14} />
                Lucknow, Uttar Pradesh
              </div>
            </div>

            {/* Key Achievements */}
            <div className="space-y-4">
              <div className="cyber-card p-4 rounded-xl border border-[hsl(191,100%,50%)]/30">
                <div className="flex items-center space-x-3">
                  <Trophy className="text-[hsl(191,100%,50%)]" size={20} />
                  <div>
                    <div className="font-orbitron text-lg font-bold text-[hsl(191,100%,50%)]">20K+</div>
                    <div className="text-sm text-gray-400">Lines of Code</div>
                  </div>
                </div>
              </div>

              <div className="cyber-card p-4 rounded-xl border border-[hsl(261,100%,67%)]/30">
                <div className="flex items-center space-x-3">
                  <Trophy className="text-[hsl(261,100%,67%)]" size={20} />
                  <div>
                    <div className="font-orbitron text-lg font-bold text-[hsl(261,100%,67%)]">98%</div>
                    <div className="text-sm text-gray-400">ML Model Accuracy</div>
                  </div>
                </div>
              </div>

              <div className="cyber-card p-4 rounded-xl border border-[hsl(213,100%,50%)]/30">
                <div className="flex items-center space-x-3">
                  <Trophy className="text-[hsl(213,100%,50%)]" size={20} />
                  <div>
                    <div className="font-orbitron text-lg font-bold text-[hsl(213,100%,50%)]">IEEE</div>
                    <div className="text-sm text-gray-400">Research Published</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Previous Experience */}
            <div className="cyber-card p-4 rounded-xl border border-[hsl(191,100%,50%)]/30">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-orbitron text-sm font-bold text-[hsl(191,100%,50%)]">
                  Previous Experience
                </h4>
                <ExternalLink className="text-gray-400" size={14} />
              </div>
              <p className="text-sm text-gray-300 font-medium">Web Dev Intern</p>
              <p className="text-xs text-gray-400">GN: Avenue IIT Bombay • June-Aug 2023</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}